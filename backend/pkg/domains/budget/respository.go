package budget

import (
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	Create(budget *entities.Budget) error
	Update(id uuid.UUID, req dtos.BudgetRequest) error
	UpdateWithUserCheck(userID uuid.UUID, id uuid.UUID, req dtos.BudgetRequest) error
	Delete(id uuid.UUID) error
	DeleteWithUserCheck(userID uuid.UUID, id uuid.UUID) error
	FindAll(userId uuid.UUID) ([]dtos.BudgetResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(budget *entities.Budget) error {
	return r.db.Create(budget).Error
}

func (r *repository) Update(id uuid.UUID, req dtos.BudgetRequest) error {
	var budget entities.Budget
	err := r.db.Model(&entities.Budget{}).Where("id = ?", id).First(&budget).Error
	if err != nil {
		return err
	}
	if req.Name != "" {
		budget.Name = req.Name
	}
	if req.Amount != 0 {
		budget.Amount = req.Amount
	}
	if req.Period != "" {
		budget.Period = req.Period
	}
	if req.CategoryId != "" {
		categoryUUID, err := uuid.Parse(req.CategoryId)
		if err != nil {
			return err
		}
		budget.CategoryId = categoryUUID
	}
	if req.Color != "" {
		budget.Color = req.Color
	}

	return r.db.Save(&budget).Error
}

func (r *repository) UpdateWithUserCheck(userID uuid.UUID, id uuid.UUID, req dtos.BudgetRequest) error {
	var budget entities.Budget
	err := r.db.Model(&entities.Budget{}).Where("id = ? AND user_id = ?", id, userID).First(&budget).Error
	if err != nil {
		return err
	}
	if req.Name != "" {
		budget.Name = req.Name
	}
	if req.Amount != 0 {
		budget.Amount = req.Amount
	}
	if req.Period != "" {
		budget.Period = req.Period
	}
	if req.CategoryId != "" {
		categoryUUID, err := uuid.Parse(req.CategoryId)
		if err != nil {
			return err
		}
		budget.CategoryId = categoryUUID
	}
	if req.Color != "" {
		budget.Color = req.Color
	}

	return r.db.Save(&budget).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Budget{}, "id = ?", id).Error
}

func (r *repository) DeleteWithUserCheck(userID uuid.UUID, id uuid.UUID) error {
	return r.db.Delete(&entities.Budget{}, "id = ? AND user_id = ?", id, userID).Error
}

func (r *repository) FindAll(userId uuid.UUID) ([]dtos.BudgetResponse, error) {
	var budgets []entities.Budget
	var transactions []entities.Transaction
	var categories []entities.Category
	var responses []dtos.BudgetResponse

	if err := r.db.Where("user_id = ?", userId).Find(&budgets).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	if err := r.db.Where("user_id = ? AND type = 'expense'", userId).Find(&transactions).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	if err := r.db.Find(&categories).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	// Create category map for quick lookup
	categoryMap := make(map[uuid.UUID]string)
	for _, cat := range categories {
		categoryMap[cat.ID] = cat.Name
	}

	for _, budget := range budgets {
		var spent float64

		for _, tx := range transactions {
			if tx.CategoryID == budget.CategoryId {
				spent += tx.Amount
			}
		}

		categoryName := categoryMap[budget.CategoryId]
		if categoryName == "" {
			categoryName = "Unknown Category"
		}

		/*responses = append(responses, dtos.BudgetResponse{
			ID:           budget.ID.String(),
			Name:         budget.Name,
			Amount:       budget.Amount,
			Period:       budget.Period,
			CategoryId:   budget.CategoryId.String(),
			CategoryName: categoryName,
			Spent:        spent,
			CreatedAt:    budget.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt:    budget.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
			Color:        budget.Color,
		})*/
	}

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Yiyecek & İçecek",
		Amount:       8000,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Yemek",
		Spent:        6420,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#10B981", // yeşil
	})

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Ulaşım",
		Amount:       4000,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Ulaşım",
		Spent:        3150,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#3B82F6", // mavi
	})

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Kira",
		Amount:       12000,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Kira",
		Spent:        12000,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#F59E0B", // turuncu
	})

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Faturalar",
		Amount:       3500,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Elektrik, Su, İnternet",
		Spent:        2980,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#EF4444", // kırmızı
	})

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Eğlence",
		Amount:       2000,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Eğlence",
		Spent:        1580,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#8B5CF6", // mor
	})

	responses = append(responses, dtos.BudgetResponse{
		ID:           uuid.New().String(),
		Name:         "Sağlık",
		Amount:       1500,
		Period:       "monthly",
		CategoryId:   uuid.New().String(),
		CategoryName: "Sağlık",
		Spent:        600,
		CreatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:    time.Now().Format("2006-01-02T15:04:05Z07:00"),
		Color:        "#14B8A6", // teal
	})

	return responses, nil
}
