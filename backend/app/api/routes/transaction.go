package routes

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/butce360/pkg/domains/transaction"
	"github.com/nocytech/butce360/pkg/dtos"
	"github.com/nocytech/butce360/pkg/middleware"
	"github.com/nocytech/butce360/pkg/state"
)

func TransactionRoutes(r *gin.RouterGroup, s transaction.Service) {
	g := r.Group("/transactions")
	g.Use(middleware.Authorized())

	g.POST("", CreateTransaction(s))
	g.POST("/bulk", CreateBulkTransactions(s))
	g.GET("", GetAllTransactions(s))
	g.GET("/:id", GetTransactionByID(s))
	g.PUT("/:id", UpdateTransaction(s))
	g.DELETE("/:id", DeleteTransaction(s))
	g.GET("/export", ExportTransactions(s))
}

// @Summary Create transaction
// @Description Create a new income or expense transaction
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.TransactionRequest true "Transaction data"
// @Success 201 {object} map[string]interface{} "Returns created transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /transactions [post]
func CreateTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.TransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateTransaction(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get all transactions
// @Description Get all transactions with optional filtering
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Filter by type (income/expense)"
// @Param start_date query string false "Filter by start date (YYYY-MM-DD)"
// @Param end_date query string false "Filter by end date (YYYY-MM-DD)"
// @Param category_id query string false "Filter by category ID"
// @Param payment_method query string false "Filter by payment method"
// @Param account_id query string false "Filter by account ID"
// @Param min_amount query number false "Filter by minimum amount"
// @Param max_amount query number false "Filter by maximum amount"
// @Param search query string false "Search in title and note"
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} map[string]interface{} "Returns list of transactions"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /transactions [get]
func GetAllTransactions(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var filter dtos.TransactionFilterRequest
		if err := c.ShouldBindQuery(&filter); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetAllTransactions(userID.String(), &filter)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get transaction by ID
// @Description Get a specific transaction by its ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} map[string]interface{} "Returns transaction details"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [get]
func GetTransactionByID(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetTransactionByID(id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update transaction
// @Description Update an existing transaction
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Param request body dtos.TransactionUpdateRequest true "Updated transaction data"
// @Success 200 {object} map[string]interface{} "Returns updated transaction"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [put]
func UpdateTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		var req dtos.TransactionUpdateRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateTransaction(id, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete transaction
// @Description Delete a transaction by ID
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Transaction ID"
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/{id} [delete]
func DeleteTransaction(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		if err := s.DeleteTransaction(id); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "transaction deleted successfully",
			"status": 200,
		})
	}
}

// @Summary Export Transactions
// @Description Export all user transactions as CSV
// @Tags transactions
// @Produce text/csv
// @Security BearerAuth
// @Success 200 {file} file "CSV file with transaction data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /transactions/export [get]
func ExportTransactions(s transaction.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := state.GetCurrentUserID(c)

		// Get user's transactions (all transactions without filter)
		filter := &dtos.TransactionFilterRequest{
			Page:  1,
			Limit: 10000, // Get all transactions
		}
		paginatedData, err := s.GetAllTransactions(userID.String(), filter)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to fetch transactions: " + err.Error(),
				"status": http.StatusInternalServerError,
			})
			return
		}

		// Build CSV data
		var csvBuilder strings.Builder
		csvBuilder.WriteString("Date,Type,Category,Amount,Description,Account\n")

		// Cast Rows to transaction slice
		transactions, ok := paginatedData.Rows.([]dtos.TransactionResponse)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":  "Failed to parse transaction data",
				"status": http.StatusInternalServerError,
			})
			return
		}

		for _, tx := range transactions {
			// Format date
			var dateStr string
			if !tx.CreatedAt.IsZero() {
				dateStr = tx.CreatedAt.Format("2006-01-02")
			} else {
				dateStr = time.Now().Format("2006-01-02")
			}

			// Build CSV row
			csvBuilder.WriteString(fmt.Sprintf("%s,%s,%s,%.2f,%s,%s\n",
				dateStr,
				tx.Type,
				tx.Category.Name,
				tx.Amount,
				tx.Note,
				tx.Account.Name,
			))
		}

		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", "attachment; filename=butce360_transactions_export.csv")
		c.String(200, csvBuilder.String())
	}
}

// @Summary Create bulk transactions
// @Description Create multiple income or expense transactions in a single request
// @Tags transactions
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.BulkTransactionRequest true "Bulk transaction data"
// @Success 201 {object} map[string]interface{} "Returns bulk transaction results"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /transactions/bulk [post]
func CreateBulkTransactions(s transaction.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.BulkTransactionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateBulkTransactions(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Return 201 if all transactions were successful, 207 (Multi-Status) if partial success
		statusCode := 201
		if resp.FailureCount > 0 {
			statusCode = 207 // Multi-Status for partial success
		}

		c.JSON(statusCode, gin.H{
			"data":   resp,
			"status": statusCode,
		})
	}
}
