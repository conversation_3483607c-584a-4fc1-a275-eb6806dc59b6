import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import InvestmentScreen from '../screens/Investment/InvestmentScreen';
import AddInvestmentSimulationScreen from '../screens/Investment/AddInvestmentSimulationScreen';
import WhatIfComparisonScreen from '../screens/Investment/WhatIfComparisonScreen';
import NavigationHeader from '../components/common/NavigationHeader';

export type InvestmentStackParamList = {
  InvestmentHome: undefined;
  AddInvestmentSimulation: undefined;
  WhatIfComparison: undefined;
};

const Stack = createNativeStackNavigator<InvestmentStackParamList>();

const InvestmentStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: true,
        header: ({ navigation, back, route }) => {
          const titleMap: Record<string, string> = {
            InvestmentHome: 'Yatırımlarım',
            AddInvestmentSimulation: 'Yatır<PERSON><PERSON>',
            WhatIfComparison: '<PERSON>ö<PERSON>?'
          };
          const title = titleMap[route.name] || 'Yatırım';
          const right = route.name === 'InvestmentHome' ? {
            rightButtonText: 'Ekle',
            onRightButtonPress: () => navigation.navigate('AddInvestmentSimulation')
          } : {};
          return (
            <NavigationHeader
              title={title}
              showBackButton={!!back}
              onBackPress={navigation.goBack}
              {...right}
            />
          );
        }
      }}
    >
      <Stack.Screen name="InvestmentHome" component={InvestmentScreen} />
      <Stack.Screen name="AddInvestmentSimulation" component={AddInvestmentSimulationScreen} />
      <Stack.Screen name="WhatIfComparison" component={WhatIfComparisonScreen} />
    </Stack.Navigator>
  );
};

export default InvestmentStackNavigator;
