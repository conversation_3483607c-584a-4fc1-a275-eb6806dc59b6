import { apiClient } from './api';
import { authService } from './authService';
import { Transaction, UpdateTransactionRequest } from '../types/models';
import { localTransactionService, LocalTransaction } from './localTransactionService';
import { localAccountService } from './localAccountService';
import { localCategoryService } from './localCategoryService';

export class TransactionService {
  private static instance: TransactionService;

  private constructor() {}

  static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService();
    }
    return TransactionService.instance;
  }


  // Get all transactions
  async getTransactions(page: number = 1, limit: number = 20): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();

      if (!token) {
        // Guest mode: return local transactions
        console.log('[TransactionService] Guest mode: returning local transactions');
        const localTransactions = await localTransactionService.getTransactions();

        // Convert LocalTransaction to Transaction format
        const transactions: Transaction[] = localTransactions.map((local: LocalTransaction) => ({
          id: local.id,
          title: local.title,
          amount: local.amount,
          type: local.type,
          currency: 'TRY',
          categoryId: local.categoryId || '',
          categoryName: local.category,
          paymentMethod: 'cash',
          accountId: local.accountId || '',
          accountName: local.account || '',
          note: local.note || '',
          transactionDate: new Date(local.date),
          createdAt: new Date(local.createdAt),
          updatedAt: new Date(local.updatedAt),
        }));

        // Apply pagination to local data
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedTransactions = transactions.slice(startIndex, endIndex);

        return {
          transactions: paginatedTransactions,
          total: transactions.length,
          page: page,
          totalPages: Math.ceil(transactions.length / limit),
        };
      }

      // Authenticated user: fetch from API
      const response = await apiClient.authenticatedRequest<{
        rows: Transaction[];
        total: number;
        page: number;
        total_pages: number;
        per_page: number;
      }>(`/transactions?page=${page}&limit=${limit}`, { method: 'GET' }, token);

      console.log('[TransactionService] Fetched transactions:', response.data);

      // Convert backend format to frontend format
      const rows = response.data.rows;
      console.log('[TransactionService] Raw rows from API:', rows);
      
      const transformedTransactions = Array.isArray(rows) ? rows.map((transaction: any) => ({
        ...transaction,
        transactionDate: transaction.transaction_date, // Map snake_case to camelCase
        categoryName: transaction.category?.name,
        accountName: transaction.account?.name,
      })) : [];

      console.log('[TransactionService] Transformed transactions:', transformedTransactions);

      return {
        transactions: transformedTransactions,
        total: response.data.total || 0,
        page: response.data.page || 1,
        totalPages: response.data.total_pages || 1,
      };
    } catch (error) {
      console.error('[TransactionService] Error fetching transactions:', error);
      // Fallback to local transactions if API fails
      console.log('[TransactionService] Falling back to local transactions');
      const localTransactions = await localTransactionService.getTransactions();
      const transactions: Transaction[] = localTransactions.map((local: LocalTransaction) => ({
        id: local.id,
        title: local.title,
        amount: local.amount,
        type: local.type,
        currency: 'TRY',
        categoryId: local.categoryId || '',
        categoryName: local.category,
        paymentMethod: 'cash',
        accountId: local.accountId || '',
        accountName: local.account || '',
        note: local.note || '',
        transactionDate: new Date(local.date),
        createdAt: new Date(local.createdAt),
        updatedAt: new Date(local.updatedAt),
      }));

      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedTransactions = transactions.slice(startIndex, endIndex);

      return {
        transactions: paginatedTransactions,
        total: transactions.length,
        page: page,
        totalPages: Math.ceil(transactions.length / limit),
      };
    }
  }

  // Get recent transactions (using regular endpoint with limit)
  async getRecentTransactions(limit: number = 5): Promise<Transaction[]> {
    try {
      const response = await this.getTransactions(1, limit);
      console.log('[TransactionService] Fetched recent transactions:', response);
      return response.transactions || [];
    } catch (error) {
      console.error('[TransactionService] Error fetching recent transactions:', error);
      // Return empty array instead of throwing error for UI stability
      return [];
    }
  }

  // Get transaction by ID
  async getTransaction(id: string): Promise<Transaction> {
    try {
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<Transaction>(`/transactions/${id}`, { method: 'GET' }, token!);

      console.log('[TransactionService] Fetched transaction:', response.data);
      return response.data;
    } catch (error) {
      console.error('[TransactionService] Error fetching transaction:', error);
      throw error;
    }
  }

  // Create new transaction
  async createTransaction(transactionData: any): Promise<Transaction> {
    try {
      // Check if user is authenticated (not guest mode)
      const token = await authService.getStoredToken();
      console.log('test ediyorum token:', token);

      if (!token) {
        // Guest mode: save to local storage
        console.log('[TransactionService] Guest mode: saving transaction locally');

        // Convert to LocalTransaction format
        const localTransaction: Omit<LocalTransaction, 'id' | 'createdAt' | 'updatedAt'> = {
          title: transactionData.title || transactionData.description || 'İşlem',
          amount: Math.abs(transactionData.amount),
          type: transactionData.type,
          category: transactionData.categoryName || 'Diğer',
          categoryId: transactionData.categoryId || transactionData.category_id,
          account: transactionData.accountName || 'Nakit',
          accountId: transactionData.accountId || transactionData.account_id, // Both formats supported
          note: transactionData.note || '',
          date: transactionData.transactionDate || transactionData.transaction_date || transactionData.date || new Date().toISOString(),
        };

        console.log('[TransactionService] Creating local transaction:', {
          categoryName: localTransaction.category,
          categoryId: localTransaction.categoryId,
          accountName: localTransaction.account,
          accountId: localTransaction.accountId,
          amount: localTransaction.amount,
          type: localTransaction.type,
          title: localTransaction.title
        });

        // Save to local storage
        const savedTransaction = await localTransactionService.addTransaction(localTransaction);

        // Update account balance by NAME (misafir mode)
        if (localTransaction.account) {
          console.log('[TransactionService] Updating account balance by name:', {
            accountName: localTransaction.account,
            amount: localTransaction.amount,
            isIncome: localTransaction.type === 'income'
          });

          await localAccountService.updateAccountBalanceByName(
            localTransaction.account,
            localTransaction.amount,
            localTransaction.type === 'income'
          );
        } else {
          console.log('[TransactionService] No account name provided, skipping balance update');
        }

        // Update category stats
        if (localTransaction.categoryId) {
          await localCategoryService.updateCategoryStats(
            localTransaction.categoryId,
            localTransaction.amount
          );
        }

        // Convert back to Transaction format for response
        const transaction: Transaction = {
          id: savedTransaction.id,
          title: savedTransaction.title,
          amount: savedTransaction.amount,
          type: savedTransaction.type,
          currency: 'TRY',
          categoryId: savedTransaction.categoryId || '',
          categoryName: savedTransaction.category,
          paymentMethod: 'cash',
          accountId: savedTransaction.accountId || '',
          accountName: savedTransaction.account || '',
          note: savedTransaction.note || '',
          transactionDate: new Date(savedTransaction.date),
          createdAt: new Date(savedTransaction.createdAt),
          updatedAt: new Date(savedTransaction.updatedAt),
        };

        return transaction;
      }

      // Authenticated user: save to API
      const authToken = await authService.getStoredToken();

      // Clean data for API
      const apiTransactionData: any = {
        title: transactionData.title,
        amount: transactionData.amount,
        note: transactionData.note,
        transaction_date: transactionData.transaction_date || transactionData.transactionDate,
        type: transactionData.type,
        payment_method: transactionData.payment_method || transactionData.paymentMethod || 'card',
        currency: transactionData.currency || 'TRY',
      };

      // Only add category_id if it's a valid UUID
      if (transactionData.category_id && !transactionData.category_id.startsWith('local_') && transactionData.category_id.length > 10) {
        apiTransactionData.category_id = transactionData.category_id;
      }

      // Only add account_id if it's a valid UUID
      if (transactionData.account_id && !transactionData.account_id.startsWith('local_') && transactionData.account_id.length > 10) {
        apiTransactionData.account_id = transactionData.account_id;
      }

      // Log the data being sent to API
      console.log('[TransactionService] Sending transaction data:', apiTransactionData);

      const response = await apiClient.authenticatedRequest<any>('/transactions', {
        method: 'POST',
        body: JSON.stringify(apiTransactionData)
      }, authToken!);

      console.log('[TransactionService] Created transaction:', response.status);

      // Backend returns { data: {...}, status: 201 } format
      return response.data?.data || response.data;
    } catch (error) {
      console.error('[TransactionService] Error creating transaction:', error);
      throw error;
    }
  }

  // Create bulk transactions
  async createBulkTransactions(transactionsData: any[]): Promise<{
    success_count: number;
    failure_count: number;
    successful: Transaction[];
    failed: Array<{
      index: number;
      error: string;
      request: any;
    }>;
  }> {
    try {
      // Check if user is authenticated (not guest mode)
      const bulkToken = await authService.getStoredToken();
      const isGuest = await authService.isGuestMode();

      if (!bulkToken || isGuest) {
        // Guest mode: save to local storage
        console.log('[TransactionService] Guest mode: saving bulk transactions locally');

        const successful: Transaction[] = [];
        const failed: Array<{ index: number; error: string; request: any }> = [];

        for (let i = 0; i < transactionsData.length; i++) {
          try {
            const transactionData = transactionsData[i];

            // Convert to LocalTransaction format
            const localTransaction: Omit<LocalTransaction, 'id' | 'createdAt' | 'updatedAt'> = {
              title: transactionData.title || transactionData.description || 'İşlem',
              amount: Math.abs(transactionData.amount),
              type: transactionData.type,
              category: transactionData.categoryName || 'Diğer',
              categoryId: transactionData.categoryId,
              account: transactionData.accountName || 'Nakit',
              accountId: transactionData.accountId,
              note: transactionData.note || '',
              date: transactionData.transactionDate || transactionData.date || new Date().toISOString(),
            };

            // Save to local storage
            const savedTransaction = await localTransactionService.addTransaction(localTransaction);

            // Update account balance
            if (localTransaction.accountId) {
              await localAccountService.updateAccountBalance(
                localTransaction.accountId,
                localTransaction.amount,
                localTransaction.type === 'income'
              );
            }

            // Update category stats
            if (localTransaction.categoryId) {
              await localCategoryService.updateCategoryStats(
                localTransaction.categoryId,
                localTransaction.amount
              );
            }

            // Convert back to Transaction format for response
            const transaction: Transaction = {
              id: savedTransaction.id,
              title: savedTransaction.title,
              amount: savedTransaction.amount,
              type: savedTransaction.type,
              currency: 'TRY',
              categoryId: savedTransaction.categoryId || '',
              categoryName: savedTransaction.category,
              paymentMethod: 'cash',
              accountId: savedTransaction.accountId || '',
              accountName: savedTransaction.account || '',
              note: savedTransaction.note || '',
              transactionDate: new Date(savedTransaction.date),
              createdAt: new Date(savedTransaction.createdAt),
              updatedAt: new Date(savedTransaction.updatedAt),
            };

            successful.push(transaction);
          } catch (error) {
            failed.push({
              index: i,
              error: error instanceof Error ? error.message : 'Unknown error',
              request: transactionsData[i],
            });
          }
        }

        return {
          success_count: successful.length,
          failure_count: failed.length,
          successful,
          failed,
        };
      }

      // Authenticated user: save to API
      const bulkAuthToken = await authService.getStoredToken();

      // Clean data for API (remove local storage fields and validate UUIDs)
      const apiTransactionsData = transactionsData.map(transaction => {
        const apiTransaction: any = {
          title: transaction.title,
          amount: transaction.amount,
          note: transaction.note,
          transaction_date: transaction.transaction_date,
          type: transaction.type,
          payment_method: transaction.payment_method,
          currency: transaction.currency,
        };

        // Only add category_id if it's a valid UUID
        if (transaction.category_id && !transaction.category_id.startsWith('local_') && transaction.category_id.length > 10) {
          apiTransaction.category_id = transaction.category_id;
        }

        // Only add account_id if it's a valid UUID
        if (transaction.account_id && !transaction.account_id.startsWith('local_') && transaction.account_id.length > 10) {
          apiTransaction.account_id = transaction.account_id;
        }

        return apiTransaction;
      });

      // Log the data being sent to API
      console.log('[TransactionService] Sending bulk transaction data:', apiTransactionsData);

      const bulkRequest = {
        transactions: apiTransactionsData
      };

      const response = await apiClient.authenticatedRequest<any>('/transactions/bulk', {
        method: 'POST',
        body: JSON.stringify(bulkRequest)
      }, bulkAuthToken!);

      console.log('[TransactionService] Created bulk transactions:', response.data);
      console.log('[TransactionService] Returning data:', response.data);
      return response.data;
    } catch (error) {
      console.error('[TransactionService] Error creating bulk transactions:', error);
      throw error;
    }
  }

  // Update transaction
  async updateTransaction(id: string, transactionData: UpdateTransactionRequest): Promise<Transaction> {
    try {
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<Transaction>(`/transactions/${id}`, {
        method: 'PUT',
        body: JSON.stringify(transactionData)
      }, token!);

      console.log('[TransactionService] Updated transaction:', response.data);
      return response.data;
    } catch (error) {
      console.error('[TransactionService] Error updating transaction:', error);
      throw error;
    }
  }

  // Delete transaction
  async deleteTransaction(id: string): Promise<void> {
    try {
      const token = await authService.getStoredToken();
      await apiClient.authenticatedRequest(`/transactions/${id}`, { method: 'DELETE' }, token!);

      console.log('[TransactionService] Deleted transaction:', id);
    } catch (error) {
      console.error('[TransactionService] Error deleting transaction:', error);
      throw error;
    }
  }

  // Transaction summary moved to ReportService

  // Get transactions by category
  async getTransactionsByCategory(categoryId: string, page: number = 1, limit: number = 20): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{
        transactions: Transaction[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/transactions/category/${categoryId}?page=${page}&limit=${limit}`, { method: 'GET' }, token!);

      console.log('[TransactionService] Fetched transactions by category:', response.data);
      return response.data;
    } catch (error) {
      console.error('[TransactionService] Error fetching transactions by category:', error);
      throw error;
    }
  }

  // Get transactions by date range
  async getTransactionsByDateRange(
    startDate: string,
    endDate: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const token = await authService.getStoredToken();
      const response = await apiClient.authenticatedRequest<{
        transactions: Transaction[];
        total: number;
        page: number;
        totalPages: number;
      }>(`/transactions/date-range?startDate=${startDate}&endDate=${endDate}&page=${page}&limit=${limit}`, { method: 'GET' }, token!);

      console.log('[TransactionService] Fetched transactions by date range:', response.data);
      return response.data;
    } catch (error) {
      console.error('[TransactionService] Error fetching transactions by date range:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const transactionService = TransactionService.getInstance();
