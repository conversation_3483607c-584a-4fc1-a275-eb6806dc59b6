import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Alert,
  ScrollView,
  Switch,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
// @ts-ignore
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import DocumentPicker from 'react-native-document-picker';
import RNFS from 'react-native-fs';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

interface ExportScreenProps {
  onNavigate?: (screen: string) => void;
}

const ExportScreen: React.FC<ExportScreenProps> = ({ }) => {
  const colors = useThemedColors();
  const styles = createStyles(colors);
  
  const [exportOptions, setExportOptions] = useState({
    includeTransactions: true,
    includeCategories: false,
    includeAccounts: false,
    includeBudgets: false,
    includeReports: true,
    dateRange: 'all', // 'all', 'thisMonth', 'thisYear', 'custom'
  });
  const [exporting, setExporting] = useState(false);

  const requestStoragePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          {
            title: 'Depolama İzni',
            message: 'PDF dosyasını kaydetmek için depolama izni gerekli',
            buttonNeutral: 'Daha Sonra Sor',
            buttonNegative: 'İptal',
            buttonPositive: 'Tamam',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const generatePDFContent = async () => {
    let content = `
      <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #3b82f6; text-align: center; }
            h2 { color: #374151; border-bottom: 2px solid #e5e7eb; padding-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; }
            th { background-color: #f3f4f6; }
            .summary { background-color: #f9fafb; padding: 15px; border-radius: 8px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1>Finansal Rapor</h1>
          <p><strong>Oluşturulma Tarihi:</strong> ${new Date().toLocaleDateString('tr-TR')}</p>
    `;

    if (exportOptions.includeTransactions) {
      const transactionsJson = await AsyncStorage.getItem('transactions');
      const transactions = transactionsJson ? JSON.parse(transactionsJson) : [];

      content += `
        <h2>İşlemler (${transactions.length} adet)</h2>
        <table>
          <tr>
            <th>Tarih</th>
            <th>Açıklama</th>
            <th>Kategori</th>
            <th>Tutar</th>
            <th>Tür</th>
          </tr>
      `;

      transactions.forEach((transaction: any) => {
        content += `
          <tr>
            <td>${new Date(transaction.date).toLocaleDateString('tr-TR')}</td>
            <td>${transaction.description}</td>
            <td>${transaction.category}</td>
            <td>${transaction.amount.toLocaleString('tr-TR')} ₺</td>
            <td>${transaction.type === 'income' ? 'Gelir' : 'Gider'}</td>
          </tr>
        `;
      });

      content += '</table>';

      // Summary
      const totalIncome = transactions.filter((t: any) => t.type === 'income').reduce((sum: number, t: any) => sum + t.amount, 0);
      const totalExpense = transactions.filter((t: any) => t.type === 'expense').reduce((sum: number, t: any) => sum + t.amount, 0);

      content += `
        <div class="summary">
          <h3>Özet</h3>
          <p><strong>Toplam Gelir:</strong> ${totalIncome.toLocaleString('tr-TR')} ₺</p>
          <p><strong>Toplam Gider:</strong> ${totalExpense.toLocaleString('tr-TR')} ₺</p>
          <p><strong>Net Bakiye:</strong> ${(totalIncome - totalExpense).toLocaleString('tr-TR')} ₺</p>
        </div>
      `;
    }

    if (exportOptions.includeCategories) {
      const categoriesJson = await AsyncStorage.getItem('categories');
      const categories = categoriesJson ? JSON.parse(categoriesJson) : [];

      content += `
        <h2>Kategoriler (${categories.length} adet)</h2>
        <table>
          <tr>
            <th>Ad</th>
            <th>Tür</th>
            <th>Renk</th>
            <th>İşlem Sayısı</th>
          </tr>
      `;

      categories.forEach((category: any) => {
        content += `
          <tr>
            <td>${category.name}</td>
            <td>${category.type === 'income' ? 'Gelir' : 'Gider'}</td>
            <td style="background-color: ${category.color}; color: white;">${category.color}</td>
            <td>${category.transactionCount || 0}</td>
          </tr>
        `;
      });

      content += '</table>';
    }

    content += `
        </body>
      </html>
    `;

    return content;
  };

  const selectSaveLocation = async () => {
    try {
      if (Platform.OS === 'ios') {
        // iOS için kullanıcıya seçenek sun
        return new Promise((resolve) => {
          Alert.alert(
            'Kaydetme Konumu Seç',
            'PDF dosyasını nereye kaydetmek istiyorsunuz?',
            [
              {
                text: 'İptal',
                style: 'cancel',
                onPress: () => resolve(null)
              },
              {
                text: 'Belgeler',
                onPress: () => resolve(RNFS.DocumentDirectoryPath)
              },
              {
                text: 'Geçici Klasör',
                onPress: () => resolve(RNFS.TemporaryDirectoryPath)
              }
            ]
          );
        });
      } else {
        // Android için kullanıcıya seçenek sun
        return new Promise((resolve) => {
          Alert.alert(
            'Kaydetme Konumu Seç',
            'PDF dosyasını nereye kaydetmek istiyorsunuz?',
            [
              {
                text: 'İptal',
                style: 'cancel',
                onPress: () => resolve(null)
              },
              {
                text: 'İndirilenler',
                onPress: () => resolve(RNFS.DownloadDirectoryPath)
              },
              {
                text: 'Belgeler',
                onPress: () => resolve(RNFS.DocumentDirectoryPath)
              },
              {
                text: 'Dış Depolama',
                onPress: () => resolve(RNFS.ExternalStorageDirectoryPath + '/Download')
              }
            ]
          );
        });
      }
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        return null; // Kullanıcı iptal etti
      }
      throw error;
    }
  };

  const handleExport = async () => {
    setExporting(true);

    try {
      // Check permissions
      const hasPermission = await requestStoragePermission();
      if (!hasPermission) {
        Alert.alert('Hata', 'Depolama izni gerekli');
        setExporting(false);
        return;
      }

      // Kullanıcıdan kaydetme konumu seç
      Alert.alert(
        'Kaydetme Konumu',
        'PDF dosyasını nereye kaydetmek istiyorsunuz?',
        [
          {
            text: 'İptal',
            style: 'cancel',
            onPress: () => setExporting(false)
          },
          {
            text: 'Konum Seç',
            onPress: async () => {
              try {
                const selectedPath = await selectSaveLocation();
                if (!selectedPath || typeof selectedPath !== 'string') {
                  setExporting(false);
                  return;
                }

                // Generate PDF content
                const htmlContent = await generatePDFContent();

                // Create PDF in temp directory first
                const fileName = `finansal-rapor-${new Date().getTime()}.pdf`;
                const tempOptions = {
                  html: htmlContent,
                  fileName: fileName.replace('.pdf', ''),
                  directory: 'Documents', // Geçici konum
                };

                const tempFile = await RNHTMLtoPDF.convert(tempOptions);

                // Move file to selected location
                let finalPath = `${selectedPath}/${fileName}`;

                try {
                  // Hedef klasörün var olduğundan emin ol
                  const dirExists = await RNFS.exists(selectedPath);
                  if (!dirExists) {
                    await RNFS.mkdir(selectedPath);
                  }

                  // Dosyayı seçilen konuma kopyala
                  await RNFS.copyFile(tempFile.filePath, finalPath);

                  // Geçici dosyayı sil
                  await RNFS.unlink(tempFile.filePath);
                } catch (copyError) {
                  console.error('File copy error:', copyError);
                  // Kopyalama başarısız olursa, geçici konumu kullan
                  finalPath = tempFile.filePath;
                }

                Alert.alert(
                  'Başarılı',
                  `PDF raporu başarıyla oluşturuldu!\nKonum: ${finalPath}`,
                  [
                    { text: 'Tamam' }
                  ]
                );
              } catch (error) {
                console.error('PDF export error:', error);
                Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu');
              } finally {
                setExporting(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('PDF export error:', error);
      Alert.alert('Hata', 'PDF oluşturulurken bir hata oluştu');
      setExporting(false);
    }
  };

  const toggleOption = (key: string) => {
    setExportOptions(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const setDateRange = (range: string) => {
    setExportOptions(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  return (
    <SafeAreaView edges={['left','right']} style={[styles.container, { backgroundColor: colors.background.primary }]}> 
      <StatusBar
        barStyle={colors.background.primary === '#1c1c1e' ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background.primary}
      />

      <ScrollView style={styles.content} contentContainerStyle={{ paddingBottom: spacing.lg }} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.section}>
          <View style={styles.infoCard}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="document-outline" size={32} color={colors.primary[500]} />
            </View>
            <Text style={styles.infoTitle}>PDF Raporu Oluştur</Text>
            <Text style={styles.infoText}>
              Tüm finansal verilerinizi detaylı PDF raporu olarak dışa aktarın.
            </Text>
          </View>
        </View>

        {/* Export Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dışa Aktarılacak Veriler</Text>
          <View style={styles.optionsList}>
            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="swap-horizontal-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>İşlemler</Text>
              </View>
              <Switch
                value={exportOptions.includeTransactions}
                onValueChange={() => toggleOption('includeTransactions')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeTransactions ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="folder-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Kategoriler</Text>
              </View>
              <Switch
                value={exportOptions.includeCategories}
                onValueChange={() => toggleOption('includeCategories')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeCategories ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="business-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Hesaplar</Text>
              </View>
              <Switch
                value={exportOptions.includeAccounts}
                onValueChange={() => toggleOption('includeAccounts')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeAccounts ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="target-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Bütçeler</Text>
              </View>
              <Switch
                value={exportOptions.includeBudgets}
                onValueChange={() => toggleOption('includeBudgets')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeBudgets ? colors.primary[500] : colors.neutral[500]}
              />
            </View>

            <View style={styles.optionItem}>
              <View style={styles.optionLeft}>
                <Ionicons name="bar-chart-outline" size={20} color={colors.text.secondary} />
                <Text style={styles.optionText}>Raporlar</Text>
              </View>
              <Switch
                value={exportOptions.includeReports}
                onValueChange={() => toggleOption('includeReports')}
                trackColor={{ false: colors.neutral[300], true: colors.primary[200] }}
                thumbColor={exportOptions.includeReports ? colors.primary[500] : colors.neutral[500]}
              />
            </View>
          </View>
        </View>

        {/* Date Range */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tarih Aralığı</Text>
          <View style={styles.dateRangeOptions}>
            {[
              { key: 'all', label: 'Tüm Veriler', icon: 'infinite-outline' },
              { key: 'thisMonth', label: 'Bu Ay', icon: 'calendar-outline' },
              { key: 'thisYear', label: 'Bu Yıl', icon: 'calendar-outline' },
              { key: 'custom', label: 'Özel Aralık', icon: 'options-outline' },
            ].map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.dateRangeOption,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionActive
                ]}
                onPress={() => setDateRange(option.key)}
              >
                <Ionicons 
                  name={option.icon} 
                  size={20} 
                  color={exportOptions.dateRange === option.key ? colors.primary[500] : colors.text.secondary} 
                />
                <Text style={[
                  styles.dateRangeOptionText,
                  exportOptions.dateRange === option.key && styles.dateRangeOptionTextActive
                ]}>
                  {option.label}
                </Text>
                {exportOptions.dateRange === option.key && (
                  <Ionicons name="checkmark-circle" size={20} color={colors.primary[500]} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Export Button */}
        <View style={styles.section}>
          <TouchableOpacity
            style={[
              styles.exportButton,
              { backgroundColor: exporting ? colors.neutral[400] : colors.primary[500] }
            ]}
            onPress={handleExport}
            disabled={exporting}
          >
            {exporting ? (
              <>
                <Ionicons name="sync-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Oluşturuluyor...
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="download-outline" size={20} color={colors.background.secondary} />
                <Text style={[styles.exportButtonText, { color: colors.background.secondary }]}>
                  PDF Olarak Dışa Aktar
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.section}>
          <View style={styles.infoBox}>
            <Ionicons name="information-circle-outline" size={20} color={colors.primary[500]} />
            <Text style={styles.infoBoxText}>
              PDF raporu tüm seçtiğiniz verileri içerecek ve cihazınıza indirilecektir.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.screenPadding,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  backButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    ...typography.styles.h4,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: spacing.screenPadding,
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  infoCard: {
    backgroundColor: colors.background.secondary,
    padding: spacing.xl,
    borderRadius: spacing.cardRadius,
    alignItems: 'center',
    marginTop: spacing.lg,
  },
  infoIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  infoTitle: {
    ...typography.styles.h5,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  infoText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsList: {
    backgroundColor: colors.background.secondary,
    borderRadius: spacing.cardRadius,
    overflow: 'hidden',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.primary,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
  },
  dateRangeOptions: {
    gap: spacing.sm,
  },
  dateRangeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderWidth: 1,
    borderColor: colors.border.primary,
  },
  dateRangeOptionActive: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  dateRangeOptionText: {
    ...typography.styles.body2,
    color: colors.text.primary,
    marginLeft: spacing.md,
    flex: 1,
  },
  dateRangeOptionTextActive: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.buttonPaddingVertical,
    borderRadius: spacing.cardRadius,
  },
  exportButtonText: {
    ...typography.styles.button,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.primary[50],
    padding: spacing.lg,
    borderRadius: spacing.cardRadius,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary[500],
  },
  infoBoxText: {
    ...typography.styles.body2,
    color: colors.text.secondary,
    marginLeft: spacing.md,
    flex: 1,
    lineHeight: 20,
  },
});

export default ExportScreen;
