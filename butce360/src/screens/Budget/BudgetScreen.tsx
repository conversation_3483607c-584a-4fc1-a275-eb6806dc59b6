import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../hooks/useAuth';
import { useThemedColors } from '../../hooks/useThemedStyles';
import { typography } from '../../theme/typography';

import { budgetService, Budget } from '../../services/budgetService';
import { formatCurrency } from '../../utils/formatters';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { Alert } from 'react-native';

interface BudgetScreenProps {
  onNavigate?: (screen: string) => void;
}

const BudgetScreen: React.FC<BudgetScreenProps> = ({ onNavigate }) => {
  const { state: authState } = useAuth();
  const colors = useThemedColors();

  // State
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch budgets from API
  const fetchBudgets = useCallback(async () => {
    try {
      setError(null);

      // Fetch data for both authenticated and guest users
      if (authState.isAuthenticated || authState.isGuest) {
        const budgetData = await budgetService.getBudgetsWithSpending();
        setBudgets(budgetData || []);
      } else {
        // For non-authenticated users, show empty state
        setBudgets([]);
      }
    } catch (fetchError) {
      console.error('[BudgetScreen] Error fetching budgets:', fetchError);
      setError(
        fetchError instanceof Error
          ? fetchError.message
          : 'Bütçeler yüklenirken hata oluştu',
      );
      setBudgets([]);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [authState.isAuthenticated, authState.isGuest]);

  // Initial load
  useEffect(() => {
    fetchBudgets();
  }, [authState.isAuthenticated, authState.isGuest, fetchBudgets]);

  // Pull to refresh
  const onRefresh = () => {
    setIsRefreshing(true);
    fetchBudgets();
  };

  const handleDeleteBudget = useCallback(async (id: string, name: string) => {
    Alert.alert(
      'Bütçeyi Sil',
      `"${name}" bütçesini silmek istediğinize emin misiniz?`,
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sil',
          style: 'destructive',
          onPress: async () => {
            try {
              await budgetService.deleteBudget(id);
              await fetchBudgets();
            } catch (e: any) {
              Alert.alert('Hata', e?.message || 'Bütçe silinirken hata oluştu');
            }
          },
        },
      ],
    );
  }, [fetchBudgets]);

  // Calculate progress percentage

  const calculateProgress = (spent: number, amount: number): number => {
    console.log(`[BudgetScreen] calculateProgress: spent=${spent}, amount=${amount}`);
    if (amount === 0) return 0;
    return Math.min((spent / amount) * 100, 100);
  };

  // Get progress color
  const getProgressColor = (progress: number): string => {
    if (progress >= 90) return '#ef4444'; // red
    if (progress >= 75) return '#f59e0b'; // yellow
    return '#22c55e'; // green
  };

  // Remove guest state check - show budgets for all users

  // Loading state
  if (isLoading) {
    return (
      <SafeAreaView
        style={[
          styles.container,
          { backgroundColor: colors.background.primary },
        ]}
      >
        <StatusBar
          barStyle={
            colors.background.primary === '#1c1c1e'
              ? 'light-content'
              : 'dark-content'
          }
          backgroundColor={colors.background.primary}
        />
        <View
          style={[
            styles.loadingContainer,
            { backgroundColor: colors.background.primary },
          ]}
        >
          <LoadingSpinner />
        </View>
      </SafeAreaView>
    );
  }

  // Authenticated user with data
  return (
    <View
      style={[styles.container, { backgroundColor: colors.background.primary }]}
    >
      <StatusBar
        barStyle={
          colors.background.primary === '#1c1c1e'
            ? 'light-content'
            : 'dark-content'
        }
        backgroundColor={colors.background.primary}
      />


      {error && (
        <View
          style={[
            styles.errorContainer,
            {
              backgroundColor: colors.error[50],
              borderLeftColor: colors.error[500],
            },
          ]}
        >
          <View style={styles.budgetRow}>
            <Ionicons
              name="alert-circle-outline"
              size={20}
              color={colors.error[500]}
              style={styles.budgetIconMargin}
            />
            <Text style={[styles.errorText, { color: colors.error[500] }]}>
              {error}
            </Text>
          </View>
          <TouchableOpacity style={styles.retryButton} onPress={fetchBudgets}>
            <View style={styles.budgetAmountRow}>
              <Ionicons
                name="refresh"
                size={16}
                color={colors.error[500]}
                style={styles.budgetAmountMargin}
              />
              <Text
                style={[styles.retryButtonText, { color: colors.error[500] }]}
              >
                Tekrar Dene
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
      >
        {budgets.length > 0 ? (
          <View style={styles.budgetList}>
            {budgets.map(budget => {
              const progress = calculateProgress(
                budget.spent ,
                budget.amount,
              );
              const progressColor = budget.color || getProgressColor(progress);
              const remaining = budget.amount - (budget.spent || 0);

              return (
                <View
                  key={budget.id}
                  style={[
                    styles.budgetItem,
                    { backgroundColor: colors.background.secondary },
                  ]}
                >
                  <View style={styles.budgetHeader}>
                    <View style={styles.budgetTitleRow}>
                      {budget.color && (
                        <View
                          style={[
                            styles.colorIndicator,
                            { backgroundColor: budget.color },
                          ]}
                        />
                      )}
                      <View style={styles.budgetTitleContainer}>
                        <Text
                          style={[
                            styles.budgetName,
                            { color: colors.text.primary },
                          ]}
                        >
                          {budget.name}
                        </Text>
                        <Text
                          style={[
                            styles.budgetCategory,
                            { color: colors.text.secondary },
                          ]}
                        >
                          {budget.categoryName || 'Kategori'}
                        </Text>
                      </View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                      <Text
                        style={[
                          styles.budgetPeriod,
                          {
                            color: colors.text.secondary,
                            backgroundColor: colors.background.tertiary,
                          },
                        ]}
                      >
                        {budget.period === 'monthly' ? 'Aylık' : 'Yıllık'}
                      </Text>
                      <TouchableOpacity
                        onPress={() => {
                          Alert.alert(
                            budget.name,
                            'Ne yapmak istiyorsunuz?',
                            [
                              { text: 'İptal', style: 'cancel' },
                              { text: 'Düzenle', onPress: () => onNavigate?.(`addBudget?budgetId=${budget.id}`) },
                              { text: 'Sil', style: 'destructive', onPress: () => handleDeleteBudget(budget.id, budget.name) },
                            ]
                          );
                        }}
                        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                      >
                        <Text style={{ fontSize: 20, color: colors.text.secondary }}>⋯</Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  <View style={styles.budgetAmounts}>
                    <Text
                      style={[
                        styles.budgetSpent,
                        { color: colors.text.primary },
                      ]}
                    >
                      Harcanan:{' '}
                      ₺{formatCurrency(budget.spent || 0, false)}
                    </Text>
                    <Text
                      style={[
                        styles.budgetTotal,
                        { color: colors.text.secondary },
                      ]}
                    >
                      / ₺{formatCurrency(budget.amount, false)}
                    </Text>
                  </View>

                  <View style={styles.progressContainer}>
                    <View
                      style={[
                        styles.progressBar,
                        { backgroundColor: colors.background.tertiary },
                      ]}
                    >
                      <View
                        style={[
                          styles.progressFill,
                          {
                            width: `${progress}%`,
                            backgroundColor: progressColor,
                          },
                        ]}
                      />
                    </View>
                    <Text
                      style={[styles.progressText, { color: progressColor }]}
                    >
                      {progress.toFixed(1)}%
                    </Text>
                  </View>

                  <View style={styles.budgetFooter}>
                    <Text
                      style={[
                        styles.remainingAmount,
                        remaining >= 0
                          ? [
                              styles.positiveAmount,
                              { color: colors.success[500] },
                            ]
                          : [
                              styles.negativeAmount,
                              { color: colors.error[500] },
                            ],
                        budget.color && { color: budget.color },
                      ]}
                    >
                      Kalan: ₺{formatCurrency(remaining, false)}
                    </Text>
                  </View>
                </View>
              );
            })}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons
              name="cash-outline"
              size={64}
              color={colors.primary[500]}
              style={styles.addBudgetIcon}
            />
            <Text style={[styles.emptyTitle, { color: colors.text.primary }]}>
              Henüz bütçe yok
            </Text>
            <Text style={[styles.emptyText, { color: colors.text.secondary }]}>
              İlk bütçenizi oluşturarak harcamalarınızı takip etmeye başlayın
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Guest State
  guestContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },

  guestTitle: {
    ...typography.styles.title2,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  guestText: {
    ...typography.styles.body,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
  },
  loginButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  loginButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },

  // Empty State
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 24,
  },
  emptyTitle: {
    ...typography.styles.title2,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyText: {
    ...typography.styles.body,
    textAlign: 'center',
    maxWidth: 280,
    marginBottom: 32,
  },

  // Empty State Button
  emptyStateButton: {
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    minHeight: 44,
  },
  emptyStateButtonText: {
    ...typography.styles.button,
    textTransform: 'none',
  },

  // Loading
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Error
  errorContainer: {
    margin: 20,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  errorText: {
    ...typography.styles.subhead,
    marginBottom: 8,
  },
  retryButton: {
    alignSelf: 'flex-start',
  },
  retryButtonText: {
    ...typography.styles.subhead,
    fontWeight: '600',
  },

  // ScrollView
  scrollView: {
    flex: 1,
  },

  // Budget List
  budgetList: {
    padding: 20,
  },
  budgetItem: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  budgetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  budgetTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  colorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  budgetName: {
    ...typography.styles.headline,
  },
  budgetTitleContainer: {
    flex: 1,
  },
  budgetCategory: {
    ...typography.styles.subhead,
    marginTop: 2,
  },
  budgetPeriod: {
    ...typography.styles.caption1,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  budgetAmounts: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  budgetSpent: {
    ...typography.styles.callout,
    fontWeight: '600',
  },
  budgetTotal: {
    ...typography.styles.callout,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressBar: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    marginRight: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    ...typography.styles.subhead,
    fontWeight: '600',
    minWidth: 45,
    textAlign: 'right',
  },
  budgetFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  remainingAmount: {
    ...typography.styles.subhead,
    fontWeight: '600',
  },
  positiveAmount: {},
  negativeAmount: {},
  guestIcon: {
    marginBottom: 24,
  },
  budgetRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetIconMargin: {
    marginRight: 8,
  },
  budgetAmountRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetAmountMargin: {
    marginRight: 4,
  },
  addBudgetIcon: {
    marginBottom: 24,
  },
});

export default BudgetScreen;
